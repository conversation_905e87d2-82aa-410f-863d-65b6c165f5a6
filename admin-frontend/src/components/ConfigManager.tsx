import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import { Upload, ArrowLeft, Plus, X, Music, Image, FileText, Loader2, Sparkles, Settings, Database, Play, Pause, Eye, Volume2, Edit, Save } from 'lucide-react'
import { apiService } from '../services/api'
import type { PageConfig } from '../types/config'

// JSON 编辑器弹窗组件
function JsonEditorModal({ 
  scriptUrl, 
  fileName, 
  isOpen, 
  onClose, 
  onSave 
}: { 
  scriptUrl: string
  fileName: string
  isOpen: boolean
  onClose: () => void
  onSave: (content: string) => Promise<void>
}) {
  const [originalContent, setOriginalContent] = useState('')
  const [editedContent, setEditedContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)

  // 加载 JSON 内容
  useEffect(() => {
    if (isOpen && scriptUrl) {
      setLoading(true)
      setLoadError(null)
      
      // 从 URL 中提取脚本路径
      const urlObj = new URL(scriptUrl)
      const scriptPath = urlObj.pathname
      
      apiService.getScriptContent(scriptPath)
        .then(response => {
          const content = response.data || response.content || ''
          try {
            // 验证是否为有效的 JSON 并格式化
            const parsed = JSON.parse(content)
            const formattedContent = JSON.stringify(parsed, null, 2)
            setOriginalContent(formattedContent)
            setEditedContent(formattedContent)
          } catch (e) {
            // 如果不是有效的 JSON，直接显示原始内容
            setOriginalContent(content)
            setEditedContent(content)
          }
        })
        .catch(error => {
          console.error('加载脚本内容失败:', error)
          setLoadError(error.message || '加载失败')
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [isOpen, scriptUrl])

  const handleSave = async () => {
    if (!editedContent.trim()) {
      alert('内容不能为空')
      return
    }

    // 验证 JSON 格式
    try {
      JSON.parse(editedContent)
    } catch (e) {
      alert('JSON 格式错误，请检查语法')
      return
    }

    setSaving(true)
    try {
      await onSave(editedContent)
      onClose()
    } catch (error) {
      console.error('保存失败:', error)
      alert('保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    setEditedContent(originalContent)
  }

  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div 
        className="relative w-full max-w-7xl h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-purple-50 to-pink-50">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-4">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">编辑 JSON 脚本</h2>
              <p className="text-gray-600 text-sm">{fileName}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              disabled={loading || saving}
              className="text-gray-600 hover:text-gray-800"
            >
              重置
            </Button>
            <Button
              onClick={handleSave}
              disabled={loading || saving || editedContent === originalContent}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              保存
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              disabled={saving}
              className="text-gray-600 hover:text-gray-800"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 flex overflow-hidden">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-500" />
                <p className="text-gray-600">正在加载脚本内容...</p>
              </div>
            </div>
          ) : loadError ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <X className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-red-600 font-medium mb-2">加载失败</p>
                <p className="text-gray-600 text-sm">{loadError}</p>
              </div>
            </div>
          ) : (
            <>
              {/* 左侧：原始内容（只读） */}
              <div className="w-1/2 border-r bg-gray-50 flex flex-col">
                <div className="p-4 border-b bg-gray-100">
                  <h3 className="font-semibold text-gray-800 flex items-center">
                    <Eye className="w-4 h-4 mr-2" />
                    原始内容 (只读)
                  </h3>
                </div>
                <div className="flex-1 overflow-auto">
                  <textarea
                    value={originalContent}
                    readOnly
                    className="w-full h-full p-4 font-mono text-sm border-0 resize-none bg-transparent text-gray-700 cursor-default"
                    spellCheck={false}
                    style={{ outline: 'none' }}
                  />
                </div>
              </div>

              {/* 右侧：编辑器 */}
              <div className="w-1/2 flex flex-col">
                <div className="p-4 border-b bg-white">
                  <h3 className="font-semibold text-gray-800 flex items-center">
                    <Edit className="w-4 h-4 mr-2" />
                    编辑内容
                    {editedContent !== originalContent && (
                      <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
                        已修改
                      </span>
                    )}
                  </h3>
                </div>
                <div className="flex-1 overflow-hidden">
                  <textarea
                    value={editedContent}
                    onChange={(e) => setEditedContent(e.target.value)}
                    className="w-full h-full p-4 font-mono text-sm border-0 resize-none focus:outline-none focus:ring-0"
                    placeholder="请输入 JSON 内容..."
                    spellCheck={false}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

// 图片预览弹窗组件
function ImagePreviewModal({ src, alt, isOpen, onClose }: { src: string; alt: string; isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div 
        className="relative max-w-4xl max-h-full bg-white rounded-2xl shadow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="bg-white/90 hover:bg-white rounded-full shadow-lg"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        <img 
          src={src} 
          alt={alt}
          className="w-full h-full object-contain max-h-[80vh]"
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-image.png'
          }}
        />
      </div>
    </div>
  )
}

// 音频播放器组件
function AudioPlayer({ src, fileName }: { src: string; fileName: string }) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null)

  const togglePlay = () => {
    if (!audio) {
      const newAudio = new Audio(src)
      newAudio.addEventListener('ended', () => setIsPlaying(false))
      newAudio.addEventListener('error', () => {
        console.error('音频加载失败:', src)
        setIsPlaying(false)
      })
      setAudio(newAudio)
      newAudio.play().then(() => setIsPlaying(true)).catch(() => setIsPlaying(false))
    } else {
      if (isPlaying) {
        audio.pause()
        setIsPlaying(false)
      } else {
        audio.play().then(() => setIsPlaying(true)).catch(() => setIsPlaying(false))
      }
    }
  }

  return (
    <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-blue-200 group hover:shadow-md transition-all duration-200">
      <div className="flex items-center flex-1 min-w-0 mr-3">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
          <Volume2 className="w-4 h-4 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <p 
            className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200 cursor-default" 
            title={fileName}
          >
            {fileName}
          </p>
        </div>
      </div>
      <Button
        size="sm"
        onClick={togglePlay}
        variant="ghost"
        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm flex-shrink-0"
      >
        {isPlaying ? (
          <Pause className="h-4 w-4" />
        ) : (
          <Play className="h-4 w-4" />
        )}
      </Button>
    </div>
  )
}

// 图片预览组件
function ImagePreview({ src, fileName, type }: { src: string; fileName: string; type: string }) {
  const [showPreview, setShowPreview] = useState(false)
  
  const getBgColorByType = (type: string) => {
    switch (type) {
      case 'card': return 'from-blue-50 to-indigo-50 border-blue-200'
      case 'detail': return 'from-green-50 to-emerald-50 border-green-200'
      default: return 'from-gray-50 to-slate-50 border-gray-200'
    }
  }

  const getIconColorByType = (type: string) => {
    switch (type) {
      case 'card': return 'from-blue-500 to-indigo-600'
      case 'detail': return 'from-green-500 to-emerald-600'
      default: return 'from-gray-500 to-slate-600'
    }
  }

  return (
    <>
      <div className={`bg-gradient-to-br ${getBgColorByType(type)} rounded-lg p-3 border group hover:shadow-md transition-all duration-200 cursor-pointer`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0 mr-3">
            <div className={`w-8 h-8 bg-gradient-to-br ${getIconColorByType(type)} rounded-lg flex items-center justify-center mr-3 shadow-sm`}>
              <Image className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p 
                className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200" 
                title={fileName}
              >
                {fileName}
              </p>
            </div>
          </div>
          <Button
            size="sm"
            onClick={() => setShowPreview(true)}
            variant="ghost"
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm flex-shrink-0"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <ImagePreviewModal 
        src={src}
        alt={fileName}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
      />
    </>
  )
}

interface ConfigManagerProps {
  onBackToPages: () => void
}

export function ConfigManager({ onBackToPages }: ConfigManagerProps) {
  const [config, setConfig] = useState<PageConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState<string | null>(null) // 正在上传的资源ID
  const [error, setError] = useState<string | null>(null)
  
  // JSON 编辑器相关状态
  const [jsonEditorOpen, setJsonEditorOpen] = useState(false)
  const [editingScript, setEditingScript] = useState<{
    categoryId: string
    itemId: number
    scriptUrl: string
    fileName: string
  } | null>(null)
  
  // 名称编辑相关状态
  const [editingName, setEditingName] = useState<{
    categoryId: string
    itemId: number
    originalName: string
    newName: string
  } | null>(null)
  const [savingName, setSavingName] = useState(false)
  
  // 按钮文案编辑相关状态
  const [editingButtonText, setEditingButtonText] = useState<{
    categoryId: string
    originalText: string
    newText: string
  } | null>(null)
  const [savingButtonText, setSavingButtonText] = useState(false)

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [pendingDelete, setPendingDelete] = useState<{
    categoryId: string
    audioIndex: number
    fileName: string
  } | null>(null)

  // 获取CDN基础URL
  const getCdnBaseUrl = () => {
    // 可以根据环境变量或其他方式判断环境
    const hostname = window.location.hostname
    if (hostname === 'localhost' || hostname.includes('test') || hostname.includes('dev')) {
      return 'https://test-cdn.moodplay.top'
    }
    return 'https://cdn.moodplay.top'
  }

  // 构建完整的资源URL
  const getResourceUrl = (path: string) => {
    if (path.startsWith('http')) {
      return path // 已经是完整URL
    }
    return `${getCdnBaseUrl()}/${path.replace(/^\//, '')}`
  }

  // 组件加载时获取tap-talk页面配置
  useEffect(() => {
    const loadTapTalkConfig = async () => {
      setError(null)
      setLoading(true)
      
      try {
        // 从API获取配置
        const response = await apiService.getPageConfig('tap-talk')
        console.log("response.data:", response.data)
        if (response.data) {
          // 检查response.data是否已经是对象
          const configData = typeof response.data === 'string' 
            ? JSON.parse(response.data) 
            : response.data
          setConfig(configData)
        } else {
          console.error('获取配置失败:', )
          throw new Error(response.message || '获取配置失败')
        }
      } catch (err) {
        console.error('获取配置失败:', err)
        setError(err instanceof Error ? err.message : '获取配置失败')
        
        // 使用mock数据作为fallback
        const { mockTapTalkConfig } = await import('../lib/utils')
        setConfig(mockTapTalkConfig)
      } finally {
        setLoading(false)
      }
    }

    loadTapTalkConfig()
  }, [])

  const handleFileUpload = async (categoryId: string, itemId: number | null, field: string, fileType: string) => {
    if (!config) return

    const input = document.createElement('input')
    input.type = 'file'
    input.accept = getFileAccept(fileType)
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file || !config) return

      const resourceId = `${categoryId}-${itemId}-${field}`
      setUploading(resourceId)
      setError(null)

      try {
        // 1. 更新配置对象中的文件路径
        const newFileName = file.name
        const newPath = `assets/pages/tap-talk/${categoryId}/${newFileName}`
        
        const updatedConfig = { ...config }
        const category = updatedConfig.tapTalks.find(c => c.category === categoryId)
        if (category && itemId !== null) {
          const item = category.items.find(i => i.id === itemId)
          if (item) {
            (item as any)[field] = newPath
          }
        }

        // 2. 调用API上传文件并更新配置
        const response = await apiService.uploadResourceAndUpdateConfig(
          'tap-talk',
          file,
          updatedConfig
        )

        if (response) {
          // 3. 更新本地状态
          setConfig(updatedConfig)
          console.log('文件上传成功:', response.data)
        } else {
          throw new Error(response.message || '上传失败')
        }
      } catch (err) {
        console.error('文件上传失败:', err)
        setError(err instanceof Error ? err.message : '文件上传失败')
      } finally {
        setUploading(null)
      }
    }
    
    input.click()
  }

  const handleAddAudio = async (categoryId: string) => {
    if (!config) return

    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'audio/*'
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file || !config) return

      const resourceId = `${categoryId}-audio-add`
      setUploading(resourceId)
      setError(null)

      try {
        // 1. 更新配置对象添加新音频
        const newPath = `assets/pages/tap-talk/${categoryId}/${file.name}`
        
        const updatedConfig = { ...config }
        const category = updatedConfig.tapTalks.find(c => c.category === categoryId)
        if (category) {
          category.audios.push(newPath)
        }

        // 2. 调用API上传文件并更新配置
        const response = await apiService.uploadResourceAndUpdateConfig(
          'tap-talk',
          file,
          updatedConfig
        )

        if (response) {
          // 3. 更新本地状态
          setConfig(updatedConfig)
          console.log('音频添加成功:', response.data)
        } else {
          throw new Error(response.message || '上传失败')
        }
      } catch (err) {
        console.error('音频上传失败:', err)
        setError(err instanceof Error ? err.message : '音频上传失败')
      } finally {
        setUploading(null)
      }
    }
    
    input.click()
  }

  // 显示删除确认对话框
  const showDeleteConfirm = (categoryId: string, audioIndex: number) => {
    if (!config) return

    const category = config.tapTalks.find(c => c.category === categoryId)
    if (!category || !category.audios[audioIndex]) return

    const audioPath = category.audios[audioIndex]
    const fileName = audioPath.split('/').pop() || '未知文件'

    setPendingDelete({
      categoryId,
      audioIndex,
      fileName
    })
    setDeleteConfirmOpen(true)
  }

  // 确认删除音频
  const handleRemoveAudio = async () => {
    if (!config || !pendingDelete) return

    setError(null)
    setLoading(true)

    try {
      const updatedConfig = { ...config }
      const category = updatedConfig.tapTalks.find(c => c.category === pendingDelete.categoryId)
      if (category) {
        category.audios.splice(pendingDelete.audioIndex, 1)
      }

      // 调用API更新配置
      const response = await apiService.updatePageConfig('tap-talk', updatedConfig)

      if (response) {
        setConfig(updatedConfig)
        console.log('音频删除成功')
        // 关闭确认对话框
        setDeleteConfirmOpen(false)
        setPendingDelete(null)
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (err) {
      console.error('音频删除失败:', err)
      setError(err instanceof Error ? err.message : '音频删除失败')
    } finally {
      setLoading(false)
    }
  }

  // 取消删除
  const cancelDelete = () => {
    setDeleteConfirmOpen(false)
    setPendingDelete(null)
  }

  // 打开 JSON 编辑器
  const handleOpenJsonEditor = (categoryId: string, itemId: number, scriptPath: string) => {
    if (!config) return
    
    setEditingScript({
      categoryId,
      itemId,
      scriptUrl: getResourceUrl(scriptPath),
      fileName: getFileName(scriptPath)
    })
    setJsonEditorOpen(true)
  }

  // 保存 JSON 脚本
  const handleSaveJsonScript = async (content: string) => {
    if (!config || !editingScript) return

    setError(null)

    try {
      // 创建 Blob 对象来模拟文件
      const blob = new Blob([content], { type: 'application/json' })
      const file = new File([blob], editingScript.fileName, { type: 'application/json' })

      // 更新配置对象中的文件路径
      const newPath = `assets/pages/tap-talk/${editingScript.categoryId}/${editingScript.fileName}`
      
      const updatedConfig = { ...config }
      const category = updatedConfig.tapTalks.find(c => c.category === editingScript.categoryId)
      if (category) {
        const item = category.items.find(i => i.id === editingScript.itemId)
        if (item) {
          item.script = newPath
        }
      }

      // 调用API上传文件并更新配置
      const response = await apiService.uploadResourceAndUpdateConfig(
        'tap-talk',
        file,
        updatedConfig
      )

      if (response) {
        // 更新本地状态
        setConfig(updatedConfig)
        console.log('JSON 脚本保存成功:', response.data)
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (err) {
      console.error('JSON 脚本保存失败:', err)
      throw err // 重新抛出错误，让弹窗组件处理
    }
  }

  // 关闭 JSON 编辑器
  const handleCloseJsonEditor = () => {
    setJsonEditorOpen(false)
    setEditingScript(null)
  }

  // 开始编辑名称
  const handleStartEditName = (categoryId: string, itemId: number, currentName: string) => {
    setEditingName({
      categoryId,
      itemId,
      originalName: currentName,
      newName: currentName
    })
  }

  // 保存名称修改
  const handleSaveName = async () => {
    if (!config || !editingName) return

    if (editingName.newName.trim() === '') {
      alert('名称不能为空')
      return
    }

    if (editingName.newName === editingName.originalName) {
      // 名称没有变化，直接取消编辑
      setEditingName(null)
      return
    }

    setSavingName(true)
    setError(null)

    try {
      // 更新配置对象中的名称
      const updatedConfig = { ...config }
      const category = updatedConfig.tapTalks.find(c => c.category === editingName.categoryId)
      if (category) {
        const item = category.items.find(i => i.id === editingName.itemId)
        if (item) {
          item.name = editingName.newName.trim()
        }
      }

      // 调用API更新配置
      const response = await apiService.updatePageConfig('tap-talk', updatedConfig)
      
      if (response) {
        // 更新本地状态
        setConfig(updatedConfig)
        setEditingName(null)
        console.log('名称修改成功')
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (err) {
      console.error('名称修改失败:', err)
      setError(err instanceof Error ? err.message : '名称修改失败')
    } finally {
      setSavingName(false)
    }
  }

  // 取消编辑名称
  const handleCancelEditName = () => {
    setEditingName(null)
  }

  // 开始编辑按钮文案
  const handleStartEditButtonText = (categoryId: string, currentText: string) => {
    setEditingButtonText({
      categoryId,
      originalText: currentText,
      newText: currentText
    })
  }

  // 保存按钮文案修改
  const handleSaveButtonText = async () => {
    if (!config || !editingButtonText) return

    if (editingButtonText.newText.trim() === '') {
      alert('按钮文案不能为空')
      return
    }

    if (editingButtonText.newText === editingButtonText.originalText) {
      // 文案没有变化，直接取消编辑
      setEditingButtonText(null)
      return
    }

    setSavingButtonText(true)
    setError(null)

    try {
      // 更新配置对象中的按钮文案
      const updatedConfig = { ...config }
      const category = updatedConfig.tapTalks.find(c => c.category === editingButtonText.categoryId)
      if (category) {
        category.buttonText = editingButtonText.newText.trim()
      }

      // 调用API更新配置
      const response = await apiService.updatePageConfig('tap-talk', updatedConfig)
      
      if (response) {
        // 更新本地状态
        setConfig(updatedConfig)
        setEditingButtonText(null)
        console.log('按钮文案修改成功')
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (err) {
      console.error('按钮文案修改失败:', err)
      setError(err instanceof Error ? err.message : '按钮文案修改失败')
    } finally {
      setSavingButtonText(false)
    }
  }

  // 取消编辑按钮文案
  const handleCancelEditButtonText = () => {
    setEditingButtonText(null)
  }

  const getFileAccept = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return 'image/*'
      case 'audio':
        return 'audio/*'
      case 'json':
        return '.json'
      default:
        return '*/*'
    }
  }

  const getFileName = (path: string) => {
    const parts = path.split('/')
    return parts[parts.length - 1]
  }

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'night': return '🌙'
      case 'daylight': return '☀️'
      case 'mantra': return '🔮'
      case 'mini-spa': return '🛁'
      default: return '📁'
    }
  }

  const getCategoryColor = (categoryId: string) => {
    switch (categoryId) {
      case 'night': return 'from-indigo-50 via-purple-50 to-blue-50 border-indigo-200 hover:from-indigo-100 hover:via-purple-100 hover:to-blue-100'
      case 'daylight': return 'from-amber-50 via-yellow-50 to-orange-50 border-amber-200 hover:from-amber-100 hover:via-yellow-100 hover:to-orange-100'
      case 'mantra': return 'from-rose-50 via-pink-50 to-purple-50 border-rose-200 hover:from-rose-100 hover:via-pink-100 hover:to-purple-100'
      case 'mini-spa': return 'from-emerald-50 via-teal-50 to-cyan-50 border-emerald-200 hover:from-emerald-100 hover:via-teal-100 hover:to-cyan-100'
      default: return 'from-slate-50 via-gray-50 to-zinc-50 border-slate-200 hover:from-slate-100 hover:via-gray-100 hover:to-zinc-100'
    }
  }

  const getCategoryAccentColor = (categoryId: string) => {
    switch (categoryId) {
      case 'night': return 'border-l-indigo-500 bg-indigo-50'
      case 'daylight': return 'border-l-amber-500 bg-amber-50'
      case 'mantra': return 'border-l-rose-500 bg-rose-50'
      case 'mini-spa': return 'border-l-emerald-500 bg-emerald-50'
      default: return 'border-l-slate-500 bg-slate-50'
    }
  }



  if (!config) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="relative z-10 p-8">
          <div className="max-w-6xl mx-auto">
            {/* 导航栏 */}
            <div className="flex items-center mb-8">
              <Button 
                variant="ghost" 
                onClick={onBackToPages} 
                className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">返回首页</span>
              </Button>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                  <span className="text-white text-xl">📱</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Tap Talk 内容配置管理
                  </h1>
                </div>
              </div>
            </div>

            {/* 状态卡片 */}
            <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 rounded-2xl">
              <CardContent className="text-center py-16">
                {loading ? (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Loader2 className="h-10 w-10 animate-spin text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">正在加载配置数据</h3>
                      <p className="text-gray-600">请稍候，正在获取页面配置信息...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Database className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无配置数据</h3>
                      <p className="text-gray-600 mb-4">该页面暂时没有可用的配置数据</p>
                      {error && (
                        <div className="bg-red-50 border border-red-200 rounded-xl p-4 max-w-md mx-auto">
                          <p className="text-red-700 font-medium">⚠️ {error}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAzIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPgo8L2c+CjwvZz4KPC9zdmc+')] opacity-40"></div>
      
      <div className="relative z-10 p-8">
        <div className="max-w-7xl mx-auto">
          {/* 头部导航 */}
          <div className="flex items-center mb-8">
            <Button 
              variant="ghost" 
              onClick={onBackToPages} 
              className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200 border border-white/50"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              <span className="font-medium">返回首页</span>
            </Button>
            <div className="flex items-center">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-xl">
                <Settings className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent">
                  Tap Talk 内容配置管理
                </h1>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-8">
              <Card className="border-red-200 bg-gradient-to-r from-red-50 to-pink-50 shadow-lg rounded-2xl overflow-hidden">
                <CardContent className="py-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                      <span className="text-white text-xl">⚠️</span>
                    </div>
                    <div>
                      <p className="text-red-800 font-medium text-lg">发生错误</p>
                      <p className="text-red-700 mt-1">{error}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 主要配置区域 */}
          <Card className="backdrop-blur-sm bg-white/90 shadow-2xl border-0 rounded-3xl overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 p-1">
              <div className="bg-white rounded-t-[calc(1.5rem-1px)]">
                <CardHeader className="text-center pb-8 pt-8">
                  <div className="flex items-center justify-center mb-6">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
                        <Sparkles className="w-8 h-8 text-white" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{config.tapTalks.length}</span>
                      </div>
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                    tap talk 配置
                  </CardTitle>
                  <CardDescription className="text-gray-600 text-base">
                    点击展开每个类别进行详细配置
                  </CardDescription>
                </CardHeader>
              </div>
            </div>
            
            <CardContent className="p-8 bg-white rounded-b-3xl">
              <Accordion type="multiple" className="w-full space-y-6">
                {config.tapTalks.map((category, index) => (
                  <AccordionItem key={category.category} value={category.category} className="border-0 shadow-lg rounded-2xl overflow-hidden">
                    <AccordionTrigger className={`text-left p-6 bg-gradient-to-r ${getCategoryColor(category.category)} hover:no-underline transition-all duration-300 hover:shadow-xl`}>
                      <div className="flex items-center justify-between w-full mr-4">
                        <div className="flex items-center">
                          <div className="w-14 h-14 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                            <span className="text-3xl">{getCategoryIcon(category.category)}</span>
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-1">{category.description}</h3>
                            <div className="flex items-center space-x-4">
                              {editingButtonText?.categoryId === category.category ? (
                                // 编辑按钮文案状态
                                <div className="flex items-center space-x-2 bg-white/80 px-3 py-2 rounded-full border border-blue-200">
                                  <span className="text-sm text-gray-600">按钮文案:</span>
                                  <input
                                    type="text"
                                    value={editingButtonText.newText}
                                    onChange={(e) => setEditingButtonText({ ...editingButtonText, newText: e.target.value })}
                                    className="text-sm font-medium text-gray-900 bg-transparent border-b border-blue-500 focus:outline-none focus:border-blue-600 min-w-0 px-1"
                                    placeholder="请输入按钮文案"
                                    autoFocus
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        handleSaveButtonText()
                                      } else if (e.key === 'Escape') {
                                        handleCancelEditButtonText()
                                      }
                                    }}
                                  />
                                  <div className="flex items-center space-x-1">
                                    <button
                                      onClick={handleSaveButtonText}
                                      disabled={savingButtonText}
                                      className="p-1 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors duration-200"
                                      title="保存"
                                    >
                                      {savingButtonText ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <Save className="h-3 w-3" />
                                      )}
                                    </button>
                                    <button
                                      onClick={handleCancelEditButtonText}
                                      disabled={savingButtonText}
                                      className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors duration-200"
                                      title="取消"
                                    >
                                      <X className="h-3 w-3" />
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                // 显示按钮文案状态
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-600 bg-white/60 px-3 py-1 rounded-full">
                                    按钮文案: "{category.buttonText}"
                                  </span>
                                  <button
                                    onClick={() => handleStartEditButtonText(category.category, category.buttonText)}
                                    disabled={savingButtonText || savingName}
                                    className="p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors duration-200"
                                    title="编辑按钮文案"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-3">
                            <div className="bg-white/60 backdrop-blur-sm rounded-full px-4 py-2">
                              <span className="text-sm font-medium text-gray-700">
                                {category.items.length} 个项目
                              </span>
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm rounded-full px-4 py-2">
                              <span className="text-sm font-medium text-gray-700">
                                {category.audios.length} 个音频
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    
                    <AccordionContent className="bg-white">
                      <div className="p-8 space-y-8">
                        {/* 背景音频管理区域 */}
                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 shadow-inner">
                          <div className="flex items-center justify-between mb-8">
                            <div className="flex items-center">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                <Music className="h-6 w-6 text-white" />
                              </div>
                              <div>
                                <h4 className="text-xl font-bold text-gray-900">背景音频管理</h4>
                                <p className="text-gray-600 mt-1">管理该类别的背景音乐文件</p>
                              </div>
                              <div className="ml-4 bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                                {category.audios.length} 个音频
                              </div>
                            </div>
                            <Button
                              onClick={() => handleAddAudio(category.category)}
                              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg rounded-xl px-6 py-3 transition-all duration-200 hover:shadow-xl"
                              disabled={uploading === `${category.category}-audio-add`}
                            >
                              {uploading === `${category.category}-audio-add` ? (
                                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                              ) : (
                                <Plus className="h-5 w-5 mr-2" />
                              )}
                              添加音频
                            </Button>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {category.audios.map((audio, index) => (
                              <div key={index} className="bg-white rounded-xl p-4 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-200">
                                <div className="space-y-3">
                                  <AudioPlayer 
                                    src={getResourceUrl(audio)}
                                    fileName={getFileName(audio)}
                                  />
                                  <div className="flex justify-end">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => showDeleteConfirm(category.category, index)}
                                      className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg shadow-sm"
                                      disabled={loading}
                                    >
                                      {loading ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <>
                                          <X className="h-4 w-4 mr-1" />
                                          <span className="text-xs">删除</span>
                                        </>
                                      )}
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* 子项配置区域 */}
                        <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl p-8 border border-gray-200 shadow-inner">
                          <div className="flex items-center mb-8">
                            <div className="w-12 h-12 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                              <FileText className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h4 className="text-xl font-bold text-gray-900">子项配置管理</h4>
                              <p className="text-gray-600 mt-1">管理该类别下的所有子项资源</p>
                            </div>
                            <div className="ml-4 bg-gray-700 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                              {category.items.length} 个子项
                            </div>
                          </div>
                          
                          <div className="space-y-12">
                            {category.items.map((item) => (
                                                              <div key={item.id} className={`${getCategoryAccentColor(category.category)} border-l-4 rounded-r-2xl shadow-lg overflow-hidden`}>
                                <div className="bg-white p-8">
                                  <div className="flex items-center justify-between mb-6">
                                    <div className="flex items-center flex-1">
                                      {editingName?.categoryId === category.category && editingName?.itemId === item.id ? (
                                        // 编辑状态
                                        <div className="flex items-center space-x-3 flex-1">
                                          <input
                                            type="text"
                                            value={editingName.newName}
                                            onChange={(e) => setEditingName({ ...editingName, newName: e.target.value })}
                                            className="text-xl font-bold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none focus:border-blue-600 flex-1 min-w-0"
                                            placeholder="请输入名称"
                                            autoFocus
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter') {
                                                handleSaveName()
                                              } else if (e.key === 'Escape') {
                                                handleCancelEditName()
                                              }
                                            }}
                                          />
                                          <div className="flex items-center space-x-2 flex-shrink-0">
                                            <Button
                                              size="sm"
                                              onClick={handleSaveName}
                                              disabled={savingName}
                                              className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                            >
                                              {savingName ? (
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                              ) : (
                                                <Save className="h-4 w-4" />
                                              )}
                                            </Button>
                                            <Button
                                              size="sm"
                                              variant="ghost"
                                              onClick={handleCancelEditName}
                                              disabled={savingName}
                                              className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                            >
                                              <X className="h-4 w-4" />
                                            </Button>
                                          </div>
                                        </div>
                                      ) : (
                                        // 显示状态
                                        <h5 className="text-xl font-bold text-gray-900">{item.name}</h5>
                                      )}
                                    </div>
                                    {(!editingName || editingName.categoryId !== category.category || editingName.itemId !== item.id) && (
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => handleStartEditName(category.category, item.id, item.name)}
                                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm ml-3"
                                        disabled={savingName}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    )}
                                  </div>
                                  
                                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                    {/* 列表图片 */}
                                    <div className="space-y-4">
                                      <div className="flex items-center">
                                        <Image className="w-5 h-5 mr-3 text-blue-600" />
                                        <span className="font-semibold text-gray-800">卡片图片</span>
                                      </div>
                                      <ImagePreview 
                                        src={getResourceUrl(item.image)}
                                        fileName={getFileName(item.image)}
                                        type="card"
                                      />
                                      <Button
                                        size="sm"
                                        onClick={() => handleFileUpload(category.category, item.id, 'image', 'image')}
                                        className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-md transition-all duration-200"
                                        disabled={uploading === `${category.category}-${item.id}-image`}
                                      >
                                        {uploading === `${category.category}-${item.id}-image` ? (
                                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        ) : (
                                          <Upload className="h-4 w-4 mr-2" />
                                        )}
                                        更换图片
                                      </Button>
                                    </div>

                                    {/* 详情背景 */}
                                    <div className="space-y-4">
                                      <div className="flex items-center">
                                        <Image className="w-5 h-5 mr-3 text-green-600" />
                                        <span className="font-semibold text-gray-800">详情背景</span>
                                      </div>
                                      <ImagePreview 
                                        src={getResourceUrl(item.detailImage)}
                                        fileName={getFileName(item.detailImage)}
                                        type="detail"
                                      />
                                      <Button
                                        size="sm"
                                        onClick={() => handleFileUpload(category.category, item.id, 'detailImage', 'image')}
                                        className="w-full bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md transition-all duration-200"
                                        disabled={uploading === `${category.category}-${item.id}-detailImage`}
                                      >
                                        {uploading === `${category.category}-${item.id}-detailImage` ? (
                                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        ) : (
                                          <Upload className="h-4 w-4 mr-2" />
                                        )}
                                        更换图片
                                      </Button>
                                    </div>

                                    {/* 脚本文件 */}
                                    <div className="space-y-4">
                                      <div className="flex items-center">
                                        <FileText className="w-5 h-5 mr-3 text-purple-600" />
                                        <span className="font-semibold text-gray-800">脚本文件</span>
                                      </div>
                                      <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-200 group hover:shadow-md transition-all duration-200">
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center flex-1 min-w-0 mr-3">
                                            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                                              <FileText className="w-4 h-4 text-white" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                              <p 
                                                className="text-sm font-medium text-gray-900 truncate group-hover:text-purple-600 transition-colors duration-200 cursor-default" 
                                                title={getFileName(item.script)}
                                              >
                                                {getFileName(item.script)}
                                              </p>
                                            </div>
                                          </div>
                                          <Button
                                            size="sm"
                                            onClick={() => handleOpenJsonEditor(category.category, item.id, item.script)}
                                            variant="ghost"
                                            className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg shadow-sm flex-shrink-0"
                                          >
                                            <Edit className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                        <Button
                                          size="sm"
                                          onClick={() => handleOpenJsonEditor(category.category, item.id, item.script)}
                                          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-lg shadow-md transition-all duration-200"
                                        >
                                          <Edit className="h-4 w-4 mr-2" />
                                          编辑脚本
                                        </Button>
                                        <Button
                                          size="sm"
                                          onClick={() => handleFileUpload(category.category, item.id, 'script', 'json')}
                                          variant="outline"
                                          className="border-purple-200 text-purple-600 hover:bg-purple-50 hover:border-purple-300 rounded-lg shadow-md transition-all duration-200"
                                          disabled={uploading === `${category.category}-${item.id}-script`}
                                        >
                                          {uploading === `${category.category}-${item.id}-script` ? (
                                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                          ) : (
                                            <Upload className="h-4 w-4 mr-2" />
                                          )}
                                          上传文件
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* JSON 编辑器弹窗 */}
      {editingScript && (
        <JsonEditorModal
          scriptUrl={editingScript.scriptUrl}
          fileName={editingScript.fileName}
          isOpen={jsonEditorOpen}
          onClose={handleCloseJsonEditor}
          onSave={handleSaveJsonScript}
        />
      )}

      {/* 删除确认对话框 */}
      {deleteConfirmOpen && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={cancelDelete}
        >
          <div
            className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-red-50 to-pink-50">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center mr-4">
                  <X className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">确认删除音频</h2>
                  <p className="text-gray-600 text-sm">此操作无法撤销</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={cancelDelete}
                disabled={loading}
                className="text-gray-600 hover:text-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* 内容区域 */}
            <div className="p-6">
              <p className="text-gray-700 mb-4">您确定要删除音频文件吗？</p>

              {pendingDelete && (
                <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-4 border border-red-200 mb-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                      <Volume2 className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {pendingDelete.fileName}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        音频文件
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* 按钮区域 */}
              <div className="flex space-x-3">
                <Button
                  variant="ghost"
                  onClick={cancelDelete}
                  disabled={loading}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl py-3"
                >
                  取消
                </Button>
                <Button
                  onClick={handleRemoveAudio}
                  disabled={loading}
                  className="flex-1 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white rounded-xl py-3 shadow-lg"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <X className="h-4 w-4 mr-2" />
                  )}
                  确认删除
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 