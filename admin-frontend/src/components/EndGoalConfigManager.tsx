import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Button } from './ui/button'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import { ArrowLeft, FileText, Loader2, Settings, Database, Edit, Save, X, Target } from 'lucide-react'
import { mockPages, mockEndGoalConfig } from '../lib/utils'
import { apiService } from '../services/api'
import type { Page } from '../types/config'
import type { EndGoalConfig } from '../types/end-goal'

// 文本编辑字段类型
type EditableField = 'description' | 'scenario' | 'example' | 'quote'

interface EndGoalConfigManagerProps {
  onBackToPages: () => void
}

export function EndGoalConfigManager({ onBackToPages }: EndGoalConfigManagerProps) {
  const [config, setConfig] = useState<EndGoalConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // 文本编辑相关状态
  const [editingText, setEditingText] = useState<{
    goalKey: string
    field: EditableField
    originalText: string
    newText: string
  } | null>(null)
  const [savingText, setSavingText] = useState(false)

  // 组件加载时获取end-goal页面配置
  useEffect(() => {
    const loadEndGoalConfig = async () => {
      setError(null)
      setLoading(true)
      
      try {
        // 从API获取配置
        const response = await apiService.getPageConfig('end-goal')
        console.log("response.data:", response.data)
        if (response.data) {
          // 检查response.data是否已经是对象
          const configData = typeof response.data === 'string' 
            ? JSON.parse(response.data) 
            : response.data
          setConfig(configData)
        } else {
          console.error('获取配置失败:', )
          throw new Error(response.message || '获取配置失败')
        }
      } catch (err) {
        console.error('获取配置失败:', err)
        setError(err instanceof Error ? err.message : '获取配置失败')
        
        // 使用mock数据作为fallback
        setConfig(mockEndGoalConfig)
      } finally {
        setLoading(false)
      }
    }

    loadEndGoalConfig()
  }, [])

  // 开始编辑文本
  const handleStartEditText = (goalKey: string, field: EditableField, currentText: string) => {
    setEditingText({
      goalKey,
      field,
      originalText: currentText,
      newText: currentText
    })
  }

  // 保存文本修改
  const handleSaveText = async () => {
    if (!config || !editingText) return

    if (editingText.newText.trim() === '') {
      alert('文本内容不能为空')
      return
    }

    if (editingText.newText === editingText.originalText) {
      // 文本没有变化，直接取消编辑
      setEditingText(null)
      return
    }

    setSavingText(true)
    setError(null)

    try {
      // 更新配置对象中的文本
      const updatedConfig = { ...config }
      const goalConfig = updatedConfig.goalConfigs.find(g => g.goalKey === editingText.goalKey)
      if (goalConfig) {
        goalConfig[editingText.field] = editingText.newText.trim()
      }

      // 调用API更新配置
      const response = await apiService.updatePageConfig('end-goal', updatedConfig)
      
      if (response) {
        // 更新本地状态
        setConfig(updatedConfig)
        setEditingText(null)
        console.log('文本修改成功')
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (err) {
      console.error('文本修改失败:', err)
      setError(err instanceof Error ? err.message : '文本修改失败')
    } finally {
      setSavingText(false)
    }
  }

  // 取消编辑文本
  const handleCancelEditText = () => {
    setEditingText(null)
  }

  // 获取字段的显示名称
  const getFieldDisplayName = (field: EditableField) => {
    switch (field) {
      case 'description': return '描述'
      case 'scenario': return '适用场景'
      case 'example': return '练习示例'
      case 'quote': return '理论依据'
      default: return '文本'
    }
  }

  // 获取字段的图标颜色
  const getFieldIconColor = (field: EditableField) => {
    switch (field) {
      case 'description': return 'from-blue-500 to-indigo-600'
      case 'scenario': return 'from-green-500 to-emerald-600'
      case 'example': return 'from-orange-500 to-amber-600'
      case 'quote': return 'from-purple-500 to-pink-600'
      default: return 'from-gray-500 to-slate-600'
    }
  }

  // 获取目标的颜色样式
  const getGoalColorStyle = (color: string) => {
    return {
      '--goal-color': color,
      backgroundColor: `${color}20`,
      borderColor: `${color}40`
    } as React.CSSProperties
  }



  if (!config) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="relative z-10 p-8">
          <div className="max-w-6xl mx-auto">
            {/* 导航栏 */}
            <div className="flex items-center mb-8">
                          <Button 
              variant="ghost" 
              onClick={onBackToPages} 
              className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              <span className="font-medium">返回首页</span>
            </Button>
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                <span className="text-white text-xl">🎯</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  频道配置管理
                </h1>
              </div>
            </div>
            </div>

            {/* 状态卡片 */}
            <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 rounded-2xl">
              <CardContent className="text-center py-16">
                {loading ? (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Loader2 className="h-10 w-10 animate-spin text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">正在加载配置数据</h3>
                      <p className="text-gray-600">请稍候，正在获取页面配置信息...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Database className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无配置数据</h3>
                      <p className="text-gray-600 mb-4">该页面暂时没有可用的配置数据</p>
                      {error && (
                        <div className="bg-red-50 border border-red-200 rounded-xl p-4 max-w-md mx-auto">
                          <p className="text-red-700 font-medium">⚠️ {error}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAzIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPgo8L2c+CjwvZz4KPC9zdmc+')] opacity-40"></div>
      
      <div className="relative z-10 p-8">
        <div className="max-w-7xl mx-auto">
          {/* 头部导航 */}
          <div className="flex items-center mb-8">
                          <Button 
                variant="ghost" 
                onClick={onBackToPages} 
                className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200 border border-white/50"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">返回首页</span>
              </Button>
              <div className="flex items-center">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-xl">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent">
                    频道配置管理
                  </h1>
                </div>
              </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-8">
              <Card className="border-red-200 bg-gradient-to-r from-red-50 to-pink-50 shadow-lg rounded-2xl overflow-hidden">
                <CardContent className="py-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                      <span className="text-white text-xl">⚠️</span>
                    </div>
                    <div>
                      <p className="text-red-800 font-medium text-lg">发生错误</p>
                      <p className="text-red-700 mt-1">{error}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 主要配置区域 */}
          <Card className="backdrop-blur-sm bg-white/90 shadow-2xl border-0 rounded-3xl overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 p-1">
              <div className="bg-white rounded-t-[calc(1.5rem-1px)]">
                <CardHeader className="text-center pb-6 pt-6">
                  <div className="flex items-center justify-center mb-4">
                    <div className="relative">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
                        <Target className="w-7 h-7 text-white" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{config.goalConfigs.length}</span>
                      </div>
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                    频道配置
                  </CardTitle>
                </CardHeader>
              </div>
            </div>
            
            <CardContent className="p-8 bg-white rounded-b-3xl">
              <Accordion type="multiple" className="w-full space-y-6">
                {config.goalConfigs.map((goal) => (
                  <AccordionItem key={goal.goalKey} value={goal.goalKey} className="border-0 shadow-lg rounded-2xl overflow-hidden">
                    <AccordionTrigger 
                      className="text-left p-6 hover:no-underline transition-all duration-300 hover:shadow-xl"
                      style={getGoalColorStyle(goal.mainColor)}
                    >
                      <div className="flex items-center justify-between w-full mr-4">
                        <div className="flex items-center">
                          <div 
                            className="w-14 h-14 backdrop-blur-sm rounded-2xl flex items-center justify-center mr-6 shadow-lg text-white font-bold text-lg"
                            style={{ backgroundColor: goal.mainColor }}
                          >
                            {goal.tag.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-1">{goal.goalKey}</h3>
                            <div className="flex items-center space-x-4">
                              <span 
                                className="text-sm text-white px-3 py-1 rounded-full font-medium"
                                style={{ backgroundColor: goal.mainColor }}
                              >
                                {goal.tag}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    
                    <AccordionContent className="bg-white">
                      <div className="p-8 space-y-8">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                          {/* 描述 */}
                          <div className="space-y-4">
                            <div className="flex items-center">
                              <div className={`w-6 h-6 bg-gradient-to-br ${getFieldIconColor('description')} rounded-lg flex items-center justify-center mr-3`}>
                                <FileText className="w-3 h-3 text-white" />
                              </div>
                              <span className="font-semibold text-gray-800">{getFieldDisplayName('description')}</span>
                            </div>
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200 min-h-[120px]">
                              {editingText?.goalKey === goal.goalKey && editingText?.field === 'description' ? (
                                // 编辑状态
                                <div className="space-y-3">
                                  <textarea
                                    value={editingText.newText}
                                    onChange={(e) => setEditingText({ ...editingText, newText: e.target.value })}
                                    className="w-full h-24 p-3 text-sm text-gray-900 bg-white border border-blue-300 rounded-lg focus:outline-none focus:border-blue-500 resize-none"
                                    placeholder="请输入描述文本"
                                    autoFocus
                                  />
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      size="sm"
                                      onClick={handleSaveText}
                                      disabled={savingText}
                                      className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                    >
                                      {savingText ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Save className="h-4 w-4" />
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={handleCancelEditText}
                                      disabled={savingText}
                                      className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                // 显示状态
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                                    {goal.description}
                                  </p>
                                  <div className="flex justify-end">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartEditText(goal.goalKey, 'description', goal.description)}
                                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm"
                                      disabled={savingText}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* 适用场景 */}
                          <div className="space-y-4">
                            <div className="flex items-center">
                              <div className={`w-6 h-6 bg-gradient-to-br ${getFieldIconColor('scenario')} rounded-lg flex items-center justify-center mr-3`}>
                                <FileText className="w-3 h-3 text-white" />
                              </div>
                              <span className="font-semibold text-gray-800">{getFieldDisplayName('scenario')}</span>
                            </div>
                            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200 min-h-[120px]">
                              {editingText?.goalKey === goal.goalKey && editingText?.field === 'scenario' ? (
                                // 编辑状态
                                <div className="space-y-3">
                                  <textarea
                                    value={editingText.newText}
                                    onChange={(e) => setEditingText({ ...editingText, newText: e.target.value })}
                                    className="w-full h-24 p-3 text-sm text-gray-900 bg-white border border-green-300 rounded-lg focus:outline-none focus:border-green-500 resize-none"
                                    placeholder="请输入适用场景文本"
                                    autoFocus
                                  />
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      size="sm"
                                      onClick={handleSaveText}
                                      disabled={savingText}
                                      className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                    >
                                      {savingText ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Save className="h-4 w-4" />
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={handleCancelEditText}
                                      disabled={savingText}
                                      className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                // 显示状态
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                                    {goal.scenario}
                                  </p>
                                  <div className="flex justify-end">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartEditText(goal.goalKey, 'scenario', goal.scenario)}
                                      className="text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg shadow-sm"
                                      disabled={savingText}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* 练习示例 */}
                          <div className="space-y-4">
                            <div className="flex items-center">
                              <div className={`w-6 h-6 bg-gradient-to-br ${getFieldIconColor('example')} rounded-lg flex items-center justify-center mr-3`}>
                                <FileText className="w-3 h-3 text-white" />
                              </div>
                              <span className="font-semibold text-gray-800">{getFieldDisplayName('example')}</span>
                            </div>
                            <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-200 min-h-[120px]">
                              {editingText?.goalKey === goal.goalKey && editingText?.field === 'example' ? (
                                // 编辑状态
                                <div className="space-y-3">
                                  <textarea
                                    value={editingText.newText}
                                    onChange={(e) => setEditingText({ ...editingText, newText: e.target.value })}
                                    className="w-full h-24 p-3 text-sm text-gray-900 bg-white border border-orange-300 rounded-lg focus:outline-none focus:border-orange-500 resize-none"
                                    placeholder="请输入练习示例文本"
                                    autoFocus
                                  />
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      size="sm"
                                      onClick={handleSaveText}
                                      disabled={savingText}
                                      className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                    >
                                      {savingText ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Save className="h-4 w-4" />
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={handleCancelEditText}
                                      disabled={savingText}
                                      className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                // 显示状态
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                                    {goal.example}
                                  </p>
                                  <div className="flex justify-end">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartEditText(goal.goalKey, 'example', goal.example)}
                                      className="text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg shadow-sm"
                                      disabled={savingText}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* 理论依据 */}
                          <div className="space-y-4">
                            <div className="flex items-center">
                              <div className={`w-6 h-6 bg-gradient-to-br ${getFieldIconColor('quote')} rounded-lg flex items-center justify-center mr-3`}>
                                <FileText className="w-3 h-3 text-white" />
                              </div>
                              <span className="font-semibold text-gray-800">{getFieldDisplayName('quote')}</span>
                            </div>
                            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200 min-h-[120px]">
                              {editingText?.goalKey === goal.goalKey && editingText?.field === 'quote' ? (
                                // 编辑状态
                                <div className="space-y-3">
                                  <textarea
                                    value={editingText.newText}
                                    onChange={(e) => setEditingText({ ...editingText, newText: e.target.value })}
                                    className="w-full h-24 p-3 text-sm text-gray-900 bg-white border border-purple-300 rounded-lg focus:outline-none focus:border-purple-500 resize-none"
                                    placeholder="请输入理论依据文本"
                                    autoFocus
                                  />
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      size="sm"
                                      onClick={handleSaveText}
                                      disabled={savingText}
                                      className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                    >
                                      {savingText ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Save className="h-4 w-4" />
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={handleCancelEditText}
                                      disabled={savingText}
                                      className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                // 显示状态
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                                    {goal.quote}
                                  </p>
                                  <div className="flex justify-end">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartEditText(goal.goalKey, 'quote', goal.quote)}
                                      className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg shadow-sm"
                                      disabled={savingText}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 